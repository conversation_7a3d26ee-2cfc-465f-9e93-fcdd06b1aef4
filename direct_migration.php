<?php

// Prevent direct access from web
if (!isset($_GET['run']) || $_GET['run'] !== 'migration') {
    die('Access denied. Use: direct_migration.php?run=migration');
}

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Direct Database Migration</h2>";
echo "<p>Running migration without CodeIgniter bootstrap...</p>";

try {
    // Load database config from .env file
    $envFile = __DIR__ . '/.env';
    if (!file_exists($envFile)) {
        throw new Exception('.env file not found');
    }
    
    $envContent = file_get_contents($envFile);
    $envLines = explode("\n", $envContent);
    $dbConfig = [];
    
    foreach ($envLines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, '#') === 0) continue;
        
        if (strpos($line, 'database.default.') === 0) {
            list($key, $value) = explode('=', $line, 2);
            $key = str_replace('database.default.', '', $key);
            $dbConfig[$key] = trim($value, '"\'');
        }
    }
    
    echo "<p>✓ Environment file loaded</p>";
    
    // Create database connection
    $host = $dbConfig['hostname'] ?? 'localhost';
    $username = $dbConfig['username'] ?? '';
    $password = $dbConfig['password'] ?? '';
    $database = $dbConfig['database'] ?? '';
    $port = $dbConfig['port'] ?? 3306;
    
    $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    echo "<p>✓ Database connection established</p>";
    
    // Check if custom_fields table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'custom_fields'");
    if ($stmt->rowCount() == 0) {
        throw new Exception('custom_fields table does not exist');
    }
    
    echo "<p>✓ custom_fields table found</p>";
    
    // Check if column already exists
    $stmt = $pdo->query("SHOW COLUMNS FROM custom_fields LIKE 'include_in_title_slug'");
    $columnExists = $stmt->rowCount() > 0;
    
    if ($columnExists) {
        echo "<p>⚠️ Column 'include_in_title_slug' already exists. Migration may have been run before.</p>";
    } else {
        echo "<p>✓ Column 'include_in_title_slug' does not exist. Proceeding with migration...</p>";
        
        // Add the column
        $sql = "ALTER TABLE custom_fields 
                ADD COLUMN include_in_title_slug TINYINT(1) NOT NULL DEFAULT 1 
                COMMENT 'Whether this custom field should be included in automatic title and slug generation'";
        
        $pdo->exec($sql);
        echo "<p>✓ Column 'include_in_title_slug' added successfully</p>";
        
        // Update existing records
        $stmt = $pdo->exec("UPDATE custom_fields SET include_in_title_slug = 1");
        echo "<p>✓ Updated {$stmt} existing custom field records</p>";
    }
    
    // Add translations
    echo "<h4>Adding translations...</h4>";
    
    // Check if language_translations table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'language_translations'");
    if ($stmt->rowCount() > 0) {
        $translations = [
            [
                'lang_id' => 1,
                'label' => 'include_in_title_slug',
                'translation' => 'Include in Title & Slug'
            ],
            [
                'lang_id' => 1,
                'label' => 'include_in_title_slug_help',
                'translation' => 'When enabled, this custom field will be used to automatically generate product titles and slugs. When disabled, this field will be excluded from automatic generation.'
            ]
        ];
        
        $addedTranslations = 0;
        $skippedTranslations = 0;
        
        foreach ($translations as $trans) {
            // Check if translation exists
            $stmt = $pdo->prepare("SELECT * FROM language_translations WHERE label = ? AND lang_id = ?");
            $stmt->execute([$trans['label'], $trans['lang_id']]);
            
            if ($stmt->rowCount() > 0) {
                echo "<p>⚠️ Translation '{$trans['label']}' already exists, skipping...</p>";
                $skippedTranslations++;
            } else {
                $stmt = $pdo->prepare("INSERT INTO language_translations (lang_id, label, translation) VALUES (?, ?, ?)");
                $stmt->execute([$trans['lang_id'], $trans['label'], $trans['translation']]);
                echo "<p>✓ Added translation: {$trans['label']}</p>";
                $addedTranslations++;
            }
        }
        
        echo "<p>✓ Translations completed: Added {$addedTranslations}, Skipped {$skippedTranslations}</p>";
    } else {
        echo "<p>⚠️ language_translations table not found, skipping translations</p>";
    }
    
    // Log migration if migrations table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'migrations'");
    if ($stmt->rowCount() > 0) {
        $stmt = $pdo->prepare("SELECT * FROM migrations WHERE version = ?");
        $stmt->execute(['2024-12-29-120000']);
        
        if ($stmt->rowCount() == 0) {
            // Get next batch
            $stmt = $pdo->query("SELECT MAX(batch) as max_batch FROM migrations");
            $result = $stmt->fetch();
            $nextBatch = ($result && $result['max_batch']) ? $result['max_batch'] + 1 : 1;
            
            $stmt = $pdo->prepare("INSERT INTO migrations (version, class, `group`, namespace, time, batch) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                '2024-12-29-120000',
                'AddIncludeInTitleSlugToCustomFields',
                'default',
                'App',
                time(),
                $nextBatch
            ]);
            
            echo "<p>✓ Migration logged in migrations table</p>";
        } else {
            echo "<p>⚠️ Migration already logged in migrations table</p>";
        }
    }
    
    echo "<h3 style='color: green;'>✅ Migration completed successfully!</h3>";
    echo "<p><strong>What was done:</strong></p>";
    echo "<ul>";
    echo "<li>Added 'include_in_title_slug' column to custom_fields table</li>";
    echo "<li>Set default value to 1 (true) for backward compatibility</li>";
    echo "<li>Updated all existing custom field records</li>";
    echo "<li>Added translation keys to language_translations table</li>";
    echo "<li>Logged migration in database</li>";
    echo "</ul>";
    
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>Access admin panel → Custom Fields to see the new option</li>";
    echo "<li>Edit existing custom fields to control title/slug inclusion</li>";
    echo "<li>Create new custom fields with granular control</li>";
    echo "<li><strong>Delete this file after use for security!</strong></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Migration failed!</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
}

echo "<hr>";
echo "<p><em>Migration script completed at " . date('Y-m-d H:i:s') . "</em></p>";
echo "<p style='color: red;'><strong>Security Notice:</strong> Please delete this file after successful migration!</p>";

?>
