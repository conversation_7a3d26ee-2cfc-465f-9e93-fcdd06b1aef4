<?php

// Prevent direct access from web
if (!isset($_GET['run']) || $_GET['run'] !== 'translations') {
    die('Access denied. Use: simple_translations.php?run=translations');
}

echo "<h2>Adding Custom Field Translations (Simple Version)</h2>";

try {
    // Bootstrap CodeIgniter
    require_once 'app/Config/Paths.php';
    $paths = new Config\Paths();
    require_once $paths->systemDirectory . '/bootstrap.php';
    
    $app = Config\Services::codeigniter();
    $app->initialize();
    
    $db = \Config\Database::connect();
    
    echo "<p>✓ Database connected</p>";
    
    // Simple translations with default lang_id = 1
    $translations = [
        [
            'lang_id' => 1,
            'label' => 'include_in_title_slug',
            'translation' => 'Include in Title & Slug'
        ],
        [
            'lang_id' => 1,
            'label' => 'include_in_title_slug_help',
            'translation' => 'When enabled, this custom field will be used to automatically generate product titles and slugs. When disabled, this field will be excluded from automatic generation.'
        ]
    ];
    
    $added = 0;
    $skipped = 0;
    
    foreach ($translations as $trans) {
        // Check if exists
        $existing = $db->query("SELECT * FROM language_translations WHERE label = ? AND lang_id = ?", 
                              [$trans['label'], $trans['lang_id']])->getRow();
        
        if ($existing) {
            echo "<p>⚠️ '{$trans['label']}' already exists, skipping...</p>";
            $skipped++;
        } else {
            if ($db->table('language_translations')->insert($trans)) {
                echo "<p>✓ Added: {$trans['label']}</p>";
                $added++;
            } else {
                echo "<p>❌ Failed to add: {$trans['label']}</p>";
            }
        }
    }
    
    echo "<h3 style='color: green;'>✅ Completed!</h3>";
    echo "<p>Added: {$added} | Skipped: {$skipped}</p>";
    echo "<p><strong>Delete this file after use!</strong></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Error: " . $e->getMessage() . "</h3>";
}

?>
