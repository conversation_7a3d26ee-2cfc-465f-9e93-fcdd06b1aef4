<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddIncludeInTitleSlugToCustomFields extends Migration
{
    public function up()
    {
        // Add the new column to custom_fields table
        $this->forge->addColumn('custom_fields', [
            'include_in_title_slug' => [
                'type'       => 'TINYINT',
                'constraint' => 1,
                'default'    => 1,
                'null'       => false,
                'comment'    => 'Whether this custom field should be included in automatic title and slug generation'
            ]
        ]);

        // Update existing records to include in title/slug generation by default (backward compatibility)
        $this->db->query("UPDATE custom_fields SET include_in_title_slug = 1 WHERE include_in_title_slug IS NULL");
    }

    public function down()
    {
        // Remove the column
        $this->forge->dropColumn('custom_fields', 'include_in_title_slug');
    }
}
