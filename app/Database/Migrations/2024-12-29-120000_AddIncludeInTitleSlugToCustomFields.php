<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddIncludeInTitleSlugToCustomFields extends Migration
{
    public function up()
    {
        // Add the new column to custom_fields table
        $this->forge->addColumn('custom_fields', [
            'include_in_title_slug' => [
                'type'       => 'TINYINT',
                'constraint' => 1,
                'default'    => 1,
                'null'       => false,
                'comment'    => 'Whether this custom field should be included in automatic title and slug generation'
            ]
        ]);

        // Update existing records to include in title/slug generation by default (backward compatibility)
        $this->db->query("UPDATE custom_fields SET include_in_title_slug = 1 WHERE include_in_title_slug IS NULL");

        // Add translations for the new field
        $this->addTranslations();
    }

    public function down()
    {
        // Remove translations
        $this->removeTranslations();

        // Remove the column
        $this->forge->dropColumn('custom_fields', 'include_in_title_slug');
    }

    private function addTranslations()
    {
        // Get all active languages
        $languages = $this->db->query("SELECT * FROM languages WHERE status = 1")->getResult();

        if (empty($languages)) {
            return;
        }

        $translations = [
            'include_in_title_slug' => [
                'en' => 'Include in Title & Slug',
                'vi' => 'Bao gồm trong Title & Slug',
                'es' => 'Incluir en Título y Slug',
                'fr' => 'Inclure dans le Titre et Slug',
                'de' => 'In Titel & Slug einbeziehen'
            ],
            'include_in_title_slug_help' => [
                'en' => 'When enabled, this custom field will be used to automatically generate product titles and slugs.',
                'vi' => 'Khi được bật, trường này sẽ được sử dụng để tự động tạo tiêu đề và slug sản phẩm.',
                'es' => 'Cuando está habilitado, este campo se utilizará para generar automáticamente títulos y slugs.',
                'fr' => 'Lorsqu\'il est activé, ce champ sera utilisé pour générer automatiquement les titres et slugs.',
                'de' => 'Wenn aktiviert, wird dieses Feld zur automatischen Generierung von Titeln und Slugs verwendet.'
            ]
        ];

        foreach ($translations as $key => $languageTexts) {
            foreach ($languages as $language) {
                $langCode = $language->language_code ?? $language->code ?? 'en';

                // Check if translation already exists
                $existing = $this->db->query("SELECT * FROM language_translations WHERE label = ? AND lang_id = ?", [$key, $language->id])->getRow();

                if (!$existing) {
                    $translationText = $languageTexts[$langCode] ?? $languageTexts['en'];

                    $this->db->table('language_translations')->insert([
                        'lang_id' => $language->id,
                        'label' => $key,
                        'translation' => $translationText
                    ]);
                }
            }
        }
    }

    private function removeTranslations()
    {
        $translationKeys = ['include_in_title_slug', 'include_in_title_slug_help'];

        foreach ($translationKeys as $key) {
            $this->db->query("DELETE FROM language_translations WHERE label = ?", [$key]);
        }
    }
}
