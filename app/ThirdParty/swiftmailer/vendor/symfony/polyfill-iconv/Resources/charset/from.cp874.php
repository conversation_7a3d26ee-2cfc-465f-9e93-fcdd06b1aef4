<?php

static $data = array (
  ' ' => ' ',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '	' => '	',
  '
' => '
',
  '' => '',
  '' => '',
  '
' => '
',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  ' ' => ' ',
  '!' => '!',
  '"' => '"',
  '#' => '#',
  '$' => '$',
  '%' => '%',
  '&' => '&',
  '\'' => '\'',
  '(' => '(',
  ')' => ')',
  '*' => '*',
  '+' => '+',
  ',' => ',',
  '-' => '-',
  '.' => '.',
  '/' => '/',
  0 => '0',
  1 => '1',
  2 => '2',
  3 => '3',
  4 => '4',
  5 => '5',
  6 => '6',
  7 => '7',
  8 => '8',
  9 => '9',
  ':' => ':',
  ';' => ';',
  '<' => '<',
  '=' => '=',
  '>' => '>',
  '?' => '?',
  '@' => '@',
  'A' => 'A',
  'B' => 'B',
  'C' => 'C',
  'D' => 'D',
  'E' => 'E',
  'F' => 'F',
  'G' => 'G',
  'H' => 'H',
  'I' => 'I',
  'J' => 'J',
  'K' => 'K',
  'L' => 'L',
  'M' => 'M',
  'N' => 'N',
  'O' => 'O',
  'P' => 'P',
  'Q' => 'Q',
  'R' => 'R',
  'S' => 'S',
  'T' => 'T',
  'U' => 'U',
  'V' => 'V',
  'W' => 'W',
  'X' => 'X',
  'Y' => 'Y',
  'Z' => 'Z',
  '[' => '[',
  '\\' => '\\',
  ']' => ']',
  '^' => '^',
  '_' => '_',
  '`' => '`',
  'a' => 'a',
  'b' => 'b',
  'c' => 'c',
  'd' => 'd',
  'e' => 'e',
  'f' => 'f',
  'g' => 'g',
  'h' => 'h',
  'i' => 'i',
  'j' => 'j',
  'k' => 'k',
  'l' => 'l',
  'm' => 'm',
  'n' => 'n',
  'o' => 'o',
  'p' => 'p',
  'q' => 'q',
  'r' => 'r',
  's' => 's',
  't' => 't',
  'u' => 'u',
  'v' => 'v',
  'w' => 'w',
  'x' => 'x',
  'y' => 'y',
  'z' => 'z',
  '{' => '{',
  '|' => '|',
  '}' => '}',
  '~' => '~',
  '' => '',
  '�' => '€',
  '�' => '…',
  '�' => '‘',
  '�' => '’',
  '�' => '“',
  '�' => '”',
  '�' => '•',
  '�' => '–',
  '�' => '—',
  '�' => ' ',
  '�' => 'ก',
  '�' => 'ข',
  '�' => 'ฃ',
  '�' => 'ค',
  '�' => 'ฅ',
  '�' => 'ฆ',
  '�' => 'ง',
  '�' => 'จ',
  '�' => 'ฉ',
  '�' => 'ช',
  '�' => 'ซ',
  '�' => 'ฌ',
  '�' => 'ญ',
  '�' => 'ฎ',
  '�' => 'ฏ',
  '�' => 'ฐ',
  '�' => 'ฑ',
  '�' => 'ฒ',
  '�' => 'ณ',
  '�' => 'ด',
  '�' => 'ต',
  '�' => 'ถ',
  '�' => 'ท',
  '�' => 'ธ',
  '�' => 'น',
  '�' => 'บ',
  '�' => 'ป',
  '�' => 'ผ',
  '�' => 'ฝ',
  '�' => 'พ',
  '�' => 'ฟ',
  '�' => 'ภ',
  '�' => 'ม',
  '�' => 'ย',
  '�' => 'ร',
  '�' => 'ฤ',
  '�' => 'ล',
  '�' => 'ฦ',
  '�' => 'ว',
  '�' => 'ศ',
  '�' => 'ษ',
  '�' => 'ส',
  '�' => 'ห',
  '�' => 'ฬ',
  '�' => 'อ',
  '�' => 'ฮ',
  '�' => 'ฯ',
  '�' => 'ะ',
  '�' => 'ั',
  '�' => 'า',
  '�' => 'ำ',
  '�' => 'ิ',
  '�' => 'ี',
  '�' => 'ึ',
  '�' => 'ื',
  '�' => 'ุ',
  '�' => 'ู',
  '�' => 'ฺ',
  '�' => '฿',
  '�' => 'เ',
  '�' => 'แ',
  '�' => 'โ',
  '�' => 'ใ',
  '�' => 'ไ',
  '�' => 'ๅ',
  '�' => 'ๆ',
  '�' => '็',
  '�' => '่',
  '�' => '้',
  '�' => '๊',
  '�' => '๋',
  '�' => '์',
  '�' => 'ํ',
  '�' => '๎',
  '�' => '๏',
  '�' => '๐',
  '�' => '๑',
  '�' => '๒',
  '�' => '๓',
  '�' => '๔',
  '�' => '๕',
  '�' => '๖',
  '�' => '๗',
  '�' => '๘',
  '�' => '๙',
  '�' => '๚',
  '�' => '๛',
);

$result =& $data;
unset($data);

return $result;
