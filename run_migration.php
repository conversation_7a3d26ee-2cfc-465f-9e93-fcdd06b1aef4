<?php

// Prevent direct access from web (optional security measure)
if (!isset($_GET['run']) || $_GET['run'] !== 'migration') {
    die('Access denied. Use: run_migration.php?run=migration');
}

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Running Custom Fields Migration</h2>";
echo "<p>Starting migration process...</p>";

try {
    // Bootstrap CodeIgniter
    require_once 'app/Config/Paths.php';
    $paths = new Config\Paths();
    
    require_once $paths->systemDirectory . '/bootstrap.php';
    
    // Initialize the application
    $app = Config\Services::codeigniter();
    $app->initialize();
    
    echo "<p>✓ CodeIgniter initialized successfully</p>";
    
    // Get database connection
    $db = \Config\Database::connect();
    
    echo "<p>✓ Database connection established</p>";
    
    // Check if custom_fields table exists
    if (!$db->tableExists('custom_fields')) {
        throw new Exception('custom_fields table does not exist');
    }
    
    echo "<p>✓ custom_fields table found</p>";
    
    // Check if column already exists
    $fields = $db->getFieldData('custom_fields');
    $columnExists = false;
    foreach ($fields as $field) {
        if ($field->name === 'include_in_title_slug') {
            $columnExists = true;
            break;
        }
    }
    
    if ($columnExists) {
        echo "<p>⚠️ Column 'include_in_title_slug' already exists. Migration may have been run before.</p>";
    } else {
        echo "<p>✓ Column 'include_in_title_slug' does not exist. Proceeding with migration...</p>";
        
        // Run the migration SQL
        $sql = "ALTER TABLE custom_fields 
                ADD COLUMN include_in_title_slug TINYINT(1) NOT NULL DEFAULT 1 
                COMMENT 'Whether this custom field should be included in automatic title and slug generation'";
        
        if ($db->query($sql)) {
            echo "<p>✓ Column 'include_in_title_slug' added successfully</p>";
        } else {
            throw new Exception('Failed to add column: ' . $db->error()['message']);
        }
        
        // Update existing records
        $updateSql = "UPDATE custom_fields SET include_in_title_slug = 1";
        if ($db->query($updateSql)) {
            $affectedRows = $db->affectedRows();
            echo "<p>✓ Updated {$affectedRows} existing custom field records</p>";
        } else {
            throw new Exception('Failed to update existing records: ' . $db->error()['message']);
        }
    }
    
    // Check if migrations table exists for logging
    if ($db->tableExists('migrations')) {
        // Check if this migration is already logged
        $existingMigration = $db->query("SELECT * FROM migrations WHERE version = '2024-12-29-120000'")->getRow();
        
        if (!$existingMigration) {
            // Get the next batch number
            $lastBatch = $db->query("SELECT MAX(batch) as max_batch FROM migrations")->getRow();
            $nextBatch = ($lastBatch && $lastBatch->max_batch) ? $lastBatch->max_batch + 1 : 1;
            
            // Log the migration
            $migrationData = [
                'version' => '2024-12-29-120000',
                'class' => 'AddIncludeInTitleSlugToCustomFields',
                'group' => 'default',
                'namespace' => 'App',
                'time' => time(),
                'batch' => $nextBatch
            ];
            
            if ($db->table('migrations')->insert($migrationData)) {
                echo "<p>✓ Migration logged in migrations table</p>";
            } else {
                echo "<p>⚠️ Warning: Could not log migration in migrations table</p>";
            }
        } else {
            echo "<p>⚠️ Migration already logged in migrations table</p>";
        }
    } else {
        echo "<p>⚠️ Warning: migrations table not found. Migration not logged.</p>";
    }
    
    echo "<h3 style='color: green;'>✅ Migration completed successfully!</h3>";
    echo "<p><strong>What was done:</strong></p>";
    echo "<ul>";
    echo "<li>Added 'include_in_title_slug' column to custom_fields table</li>";
    echo "<li>Set default value to 1 (true) for backward compatibility</li>";
    echo "<li>Updated all existing custom field records</li>";
    echo "<li>Logged migration in database (if migrations table exists)</li>";
    echo "</ul>";
    
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>You can now access the admin panel to configure custom fields</li>";
    echo "<li>Edit existing custom fields to control title/slug inclusion</li>";
    echo "<li>Create new custom fields with granular control</li>";
    echo "<li><strong>Remember to delete this file after use for security!</strong></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Migration failed!</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    
    if (isset($db)) {
        $error = $db->error();
        if (!empty($error['message'])) {
            echo "<p><strong>Database Error:</strong> " . $error['message'] . "</p>";
        }
    }
}

echo "<hr>";
echo "<p><em>Migration script completed at " . date('Y-m-d H:i:s') . "</em></p>";
echo "<p style='color: red;'><strong>Security Notice:</strong> Please delete this file after successful migration!</p>";

?>
