<?php

// Prevent direct access from web
if (!isset($_GET['run']) || $_GET['run'] !== 'translations') {
    die('Access denied. Use: add_translations.php?run=translations');
}

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Adding Custom Field Translations</h2>";
echo "<p>Starting translation addition process...</p>";

try {
    // Bootstrap CodeIgniter
    require_once 'app/Config/Paths.php';
    $paths = new Config\Paths();
    
    require_once $paths->systemDirectory . '/bootstrap.php';
    
    // Initialize the application
    $app = Config\Services::codeigniter();
    $app->initialize();
    
    echo "<p>✓ CodeIgniter initialized successfully</p>";
    
    // Get database connection
    $db = \Config\Database::connect();
    
    echo "<p>✓ Database connection established</p>";
    
    // Check if language_translations table exists
    if (!$db->tableExists('language_translations')) {
        throw new Exception('language_translations table does not exist');
    }
    
    echo "<p>✓ language_translations table found</p>";
    
    // Get all active languages
    $languages = $db->query("SELECT * FROM languages WHERE status = 1")->getResult();
    
    if (empty($languages)) {
        throw new Exception('No active languages found');
    }
    
    echo "<p>✓ Found " . count($languages) . " active languages</p>";
    
    // Define translations for each language
    $translations = [
        'include_in_title_slug' => [
            'en' => 'Include in Title & Slug',
            'vi' => 'Bao gồm trong Title & Slug',
            'es' => 'Incluir en Título y Slug',
            'fr' => 'Inclure dans le Titre et Slug',
            'de' => 'In Titel & Slug einbeziehen',
            'ar' => 'تضمين في العنوان والرابط',
            'tr' => 'Başlık ve Slug\'a Dahil Et',
            'pt' => 'Incluir no Título e Slug'
        ],
        'include_in_title_slug_help' => [
            'en' => 'When enabled, this custom field will be used to automatically generate product titles and slugs. When disabled, this field will be excluded from automatic generation.',
            'vi' => 'Khi được bật, trường tùy chỉnh này sẽ được sử dụng để tự động tạo tiêu đề và slug sản phẩm. Khi tắt, trường này sẽ bị loại trừ khỏi việc tạo tự động.',
            'es' => 'Cuando está habilitado, este campo personalizado se utilizará para generar automáticamente títulos y slugs de productos. Cuando está deshabilitado, este campo se excluirá de la generación automática.',
            'fr' => 'Lorsqu\'il est activé, ce champ personnalisé sera utilisé pour générer automatiquement les titres et slugs des produits. Lorsqu\'il est désactivé, ce champ sera exclu de la génération automatique.',
            'de' => 'Wenn aktiviert, wird dieses benutzerdefinierte Feld zur automatischen Generierung von Produkttiteln und Slugs verwendet. Wenn deaktiviert, wird dieses Feld von der automatischen Generierung ausgeschlossen.',
            'ar' => 'عند التفعيل، سيتم استخدام هذا الحقل المخصص لإنشاء عناوين المنتجات والروابط تلقائياً. عند التعطيل، سيتم استبعاد هذا الحقل من الإنشاء التلقائي.',
            'tr' => 'Etkinleştirildiğinde, bu özel alan ürün başlıklarını ve slug\'ları otomatik olarak oluşturmak için kullanılacaktır. Devre dışı bırakıldığında, bu alan otomatik oluşturmadan hariç tutulacaktır.',
            'pt' => 'Quando habilitado, este campo personalizado será usado para gerar automaticamente títulos e slugs de produtos. Quando desabilitado, este campo será excluído da geração automática.'
        ]
    ];
    
    $addedCount = 0;
    $skippedCount = 0;
    
    foreach ($translations as $key => $languageTexts) {
        echo "<h4>Processing translation key: {$key}</h4>";
        
        foreach ($languages as $language) {
            $langCode = $language->language_code ?? $language->code ?? 'en';
            
            // Check if translation already exists
            $existing = $db->query("SELECT * FROM language_translations WHERE label = ? AND lang_id = ?", [$key, $language->id])->getRow();
            
            if ($existing) {
                echo "<p>⚠️ Translation for '{$key}' in {$langCode} already exists, skipping...</p>";
                $skippedCount++;
                continue;
            }
            
            // Get translation text for this language, fallback to English
            $translationText = $languageTexts[$langCode] ?? $languageTexts['en'];
            
            // Insert translation
            $data = [
                'lang_id' => $language->id,
                'label' => $key,
                'translation' => $translationText
            ];
            
            if ($db->table('language_translations')->insert($data)) {
                echo "<p>✓ Added translation for '{$key}' in {$langCode}: {$translationText}</p>";
                $addedCount++;
            } else {
                echo "<p>❌ Failed to add translation for '{$key}' in {$langCode}</p>";
            }
        }
    }
    
    echo "<h3 style='color: green;'>✅ Translation addition completed!</h3>";
    echo "<p><strong>Summary:</strong></p>";
    echo "<ul>";
    echo "<li>Added: {$addedCount} translations</li>";
    echo "<li>Skipped (already exists): {$skippedCount} translations</li>";
    echo "</ul>";
    
    echo "<p><strong>Translation keys added:</strong></p>";
    echo "<ul>";
    foreach (array_keys($translations) as $key) {
        echo "<li><code>{$key}</code></li>";
    }
    echo "</ul>";
    
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>The admin interface will now display proper translations</li>";
    echo "<li>You can edit these translations in the admin language management</li>";
    echo "<li><strong>Remember to delete this file after use for security!</strong></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Translation addition failed!</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    
    if (isset($db)) {
        $error = $db->error();
        if (!empty($error['message'])) {
            echo "<p><strong>Database Error:</strong> " . $error['message'] . "</p>";
        }
    }
}

echo "<hr>";
echo "<p><em>Translation script completed at " . date('Y-m-d H:i:s') . "</em></p>";
echo "<p style='color: red;'><strong>Security Notice:</strong> Please delete this file after successful execution!</p>";

?>
